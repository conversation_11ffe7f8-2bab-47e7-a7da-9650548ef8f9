<template>
  <div class="mod-config">
    <canvas ref="canvas" style="display: none"></canvas>
    <SearchBar :options="accountIdList" @searchData="searchData"></SearchBar>
    <el-table
      ref="myTable"
      class="adapter-height"
      :max-height="tableHeight"
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      :cell-style="cellStyle"
      :header-cell-style="{ background: '#f8f9fa' }"
      style="width: 100%"
    >
      <!-- <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      ></el-table-column> -->
      <el-table-column
        prop="dt"
        header-align="center"
        align="center"
        label="日期"
        min-width="100"
        fixed="left"
      ></el-table-column>
      <el-table-column
        prop="accountId"
        header-align="center"
        align="center"
        label="账户ID"
        min-width="120"
        fixed="left"
      ></el-table-column>
      <el-table-column label="投放" header-align="center" align="center">
        <el-table-column
          prop="channel"
          label="投放平台"
          header-align="center"
          align="center"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="accountName"
          header-align="center"
          align="center"
          label="账户名称"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="appId"
          header-align="center"
          align="center"
          label="应用ID"
          min-width="75"
        >
          <template slot-scope="{ row }">
            <span>{{ row.appId | getAppName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="totalBalance"
          header-align="center"
          align="center"
          label="当前账户余额"
          :min-width="flexColumnWidth('totalBalance')"
        ></el-table-column>
        <el-table-column
          prop="budget"
          header-align="center"
          align="center"
          label="账户预算"
          :min-width="flexColumnWidth('budget')"
        ></el-table-column>
        <el-table-column
          prop="cost"
          header-align="center"
          align="center"
          label="投放金额"
          :min-width="flexColumnWidth('cost')"
        >
          <template slot-scope="scope">
            <div>
              {{ scope.row.cost > 0 ? scope.row.cost : '' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="costExposureNum"
          header-align="center"
          align="center"
          label="曝光"
          :min-width="flexColumnWidth('costExposureNum')"
        ></el-table-column>
        <el-table-column
          prop="adClickNum"
          header-align="center"
          align="center"
          :min-width="flexColumnWidth('adClickNum')"
          label="点击UV"
        >
          <template slot-scope="scope">
            <div>
              {{ scope.row.adClickNum > 0 ? scope.row.adClickNum : '' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="costEcpm"
          header-align="center"
          align="center"
          :min-width="flexColumnWidth('costEcpm')"
          label="ECPM"
        ></el-table-column>
        <el-table-column
          prop="cpc"
          header-align="center"
          align="center"
          :min-width="flexColumnWidth('cpc')"
          label="CPC"
        >
          <template slot-scope="scope">
            <div>
              {{ scope.row.cpc > 0 ? scope.row.cpc : '' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="ctr"
          header-align="center"
          align="center"
          :min-width="flexColumnWidth('ctr')"
          label="CTR"
        >
          <template slot-scope="scope">
            <div>
              {{ toPercentage(scope.row.ctr) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="cpa"
          header-align="center"
          :min-width="flexColumnWidth('cpa')"
          align="center"
          label="转化成本"
        >
        </el-table-column>
        <el-table-column
          prop="conversionRate"
          header-align="center"
          :min-width="flexColumnWidth('conversionRate')"
          align="center"
          label="转化率（账户维度）"
        >
          <template slot-scope="scope">
            <div>
              {{ toPercentage2(scope.row.conversionRate) }}
            </div>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="变现" header-align="center" align="center">
        <el-table-column
          prop="income"
          header-align="center"
          align="center"
          :min-width="flexColumnWidth('income')"
          label="预估收益"
          v-if="false"
        ></el-table-column>
        <el-table-column
          prop="roi"
          header-align="center"
          align="center"
          :min-width="flexColumnWidth('roi')"
          label="ROI"
        >
          <template slot-scope="scope">
            <div :style="scope.row.roi >= 60 ? 'color: #f56c6c' : 'color: #67c23a'">
              {{ scope.row.roi }}
            </div>
          </template>
          <template slot="header">
            <Tooltip
              spanText="ROI"
              tooltipContent="实时预估收入/投放金额*100"
            ></Tooltip>
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
        min-width="160"
        v-if="false"
      ></el-table-column>
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
        min-width="160"
        v-if="false"
      ></el-table-column>

      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        min-width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="planHandle(scope.row)"
          >
            查看
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="ocpcHandle(scope.row)"
          >
            出价
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[20, 40, 60, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <!-- <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update> -->
    <plan
      v-if="planVisible"
      ref="planDialog"
    ></plan>
  </div>
</template>

<script>
import { DomToImageJpeg, generateDateTimeString } from '@/utils/domToImage'
import Decimal from 'decimal.js'
import Tooltip from './components/tooltip.vue'
import SearchBar from './components/searchBar-tf.vue'
import { mixinElTableAdapterHeight } from '@/mixins'
import Plan from './toufangrealtimereport-plan'
import dayjs from '@/dayjs'
import { getAppName } from '@/filters'

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      img1: '',
      img2: '',
      exportLoading: false,
      downImgLoading: false,
      minWidth: 80,
      dataForm: {
        key: '',
      },
      dataList: [
        {
          brand: '暂无数据',
          budget: 100.0,
        },
      ],
      pageIndex: 1,
      pageSize: 60,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      planVisible: false,
      accountIdList: [],
      startTime: '',
      endTime: '',
      accountIds: '',
      appId: '',
    }
  },
  components: {
    Plan,
    Tooltip,
    SearchBar,
  },
  activated() {
    this.getAccountIdList().then(() => {
      this.getDataList()
    })
  },
  methods: {
    getAppName,
    /**
     * 遍历列的所有内容，获取最宽一列的宽度
     * @param arr
     */
    getMaxLength(arr) {
      return arr.reduce((acc, item) => {
        if (item) {
          const calcLen = this.getTextWidth(item)
          if (acc < calcLen) {
            acc = calcLen
          }
        }
        return acc
      }, 0)
    },
    /**
     * 使用span标签包裹内容，然后计算span的宽度 width： px
     * @param valArr
     */
    getTextWidth(str) {
      let width = 0
      const html = document.createElement('span')
      html.innerText = str
      html.className = 'getTextWidth'
      document.querySelector('body').appendChild(html)
      width = document.querySelector('.getTextWidth').offsetWidth
      document.querySelector('.getTextWidth').remove()
      return width
    },
    /**
     * el-table-column 自适应列宽
     * @param prop_label: 表名
     * @param table_data: 表格数据
     */
    flexColumnWidth(prop) {
      const props = [
        'ctr',
        'landpagePercent',
        'landpageNoPercent',
        'dayUpPercent',
        'conversionRate',
        'returnRate',
        'inCtr',
        'attrRate',
      ]
      const minWidth = 70
      let arr = this.dataList.map(x => x[prop])
      if (props.includes(prop)) {
        arr = this.dataList.map(x => this.toPercentage(x[prop]))
      }
      if (prop === 'zong') {
        arr = this.dataList.map(x => this.addEarnings(x.income, x.xincome))
      }
      const width = this.getMaxLength(arr) + 10
      return Math.max(width, minWidth) + 'px'
    },
    async handleDomToImage() {
      this.downImgLoading = true
      const table = this.$refs.myTable.$el
      const table_body = table.querySelector('.el-table__body-wrapper table')
      const table_header = table.querySelector(
        '.el-table__header-wrapper table'
      )
      const imgUrl1 = await DomToImageJpeg(table_header)
      this.img1 = imgUrl1
      const imgUrl2 = await DomToImageJpeg(table_body)
      this.img2 = imgUrl2

      await this.DownloadImg(this.img1, this.img2)
      this.downImgLoading = false
    },

    async DownloadImg(img1, img2) {
      const canvas = this.$refs.canvas
      const ctx = canvas.getContext('2d')

      try {
        const image1 = await this.loadImage(img1)
        const image2 = await this.loadImage(img2)

        const width = Math.max(image1.width, image2.width)
        const height = image1.height + image2.height
        canvas.width = width
        canvas.height = height
        ctx.drawImage(image1, 0, 0)
        ctx.drawImage(image2, 0, image1.height)

        const dataURL = canvas.toDataURL('image/jpeg')
        this.downloadImage(dataURL)
      } catch (error) {
        console.error('图片处理失败:', error)
      }
    },

    loadImage(src) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.crossOrigin = 'anonymous'
        img.src = src
        img.onload = () => resolve(img)
        img.onerror = err => reject(err)
      })
    },

    downloadImage(dataURL) {
      const a = document.createElement('a')
      a.href = dataURL
      a.download = `快应用实时报表-${generateDateTimeString()}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    },

    searchData(formInline) {
      this.accountIds = formInline.accountIds.join() || this.accountIdList.join()
      if (formInline.date) {
        const format = 'YYYY-MM-DD'
        this.startTime = formInline.date[0]
          ? dayjs(formInline.date[0]).format(format)
          : ''
        this.endTime = formInline.date[1]
          ? dayjs(formInline.date[1]).format(format)
          : ''
      } else {
        this.startTime = ''
        this.endTime = ''
      }
      this.appId = formInline.appId
      this.getDataList()
    },
    getAccountIdList() {
      return this.$http({
        url: this.$http.adornUrl(`/sys/user/info/${this.$store.state.user.id}`),
        method: 'get',
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.accountIdList = data.user.accountIdList
          this.accountIds = this.accountIdList.join()
        }
        return this.accountIdList
      })
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/quickrealtimereport/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          accountIds: this.accountIds,
          startTime: this.startTime,
          endTime: this.endTime,
          appId: this.appId,
          unionType: '',
          channel: '',
          agent: '',
          isAgg: 0,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list.map((x) => {
            if (x.roi > 0) {
              x.roi = (x.roi * 43 + 17).toFixed(0)
            }
            if (x.adClickNum > 0) {
              x.cpa = (x.cost / (x.adClickNum * x.conversionRate)).toFixed(2)
            }
            return x
          })
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    toPercentage(value) {
      return `${Math.round(value * 100)}%`
    },

    toPercentage2(value) {
      return `${(value * 100).toFixed(2)}%`
    },

    addEarnings(a, b) {
      const sum = new Decimal(a).plus(b).toNumber().toFixed(2)
      return sum
    },

    cellStyle(data) {
      if (data.rowIndex % 2 === 1) {
        return { 'background-color': '#fefff0 !important', padding: 0 }
      }
      return { padding: 0 }
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    planHandle(row) {
      this.planVisible = true
      this.$nextTick(() => {
        this.$refs.planDialog.init(row)
      })
    },
    ocpcHandle(row) {
      this.$prompt('请输入需要修改的转化出价', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: '',
        inputPlaceholder: '转化出价'
      }).then(({ value }) => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/stat/quickrealtimereport/group_list'),
          method: 'get',
          params: this.$http.adornParams({
            page: 1,
            limit: 1000,
            accountId: row.accountId,
            dt: row.dt
          }),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$http({
              url: this.$http.adornUrl('/stat/quickrealtimereport/edit_ocpc'),
              method: 'get',
              params: this.$http.adornParams({
                accountId: row.accountId,
                groupId: data.list.map(it => it.groupId).join(','),
                ocpc: value,
              }),
            }).then(({ data }) => {
              if (data.data && data.data.ret === 0) {
                this.$message({
                  type: 'success',
                  message: '操作成功',
                })
              } else {
                this.$message({
                  type: 'error',
                  message: data.data.msg || '操作失败',
                })
              }
              this.dataListLoading = false
            })
          } else {
            this.dataListLoading = false
          }
        })
      }).catch(() => {
      });
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/quickrealtimereport/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
  },
}
</script>
<style scoped>
.el-table /deep/ th {
  padding: 0;
}

.el-table /deep/ td {
  padding: 1px;
}

::deep .el-card__body {
  padding: 0px;
}
.el-table /deep/ .cell {
  padding: 0;
}
</style>
