<template>
  <div class="table-test-page">
    <div class="page-header">
      <h1>表格样式测试</h1>
      <p>验证Element UI表格组件的样式是否正常</p>
    </div>

    <!-- 基础表格 -->
    <el-card class="test-card">
      <div slot="header">
        <span>基础表格</span>
      </div>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="age" label="年龄" />
        <el-table-column prop="address" label="地址" />
      </el-table>
    </el-card>

    <!-- 带边框和斑马纹的表格 -->
    <el-card class="test-card">
      <div slot="header">
        <span>边框 + 斑马纹表格</span>
      </div>
      <el-table :data="tableData" style="width: 100%" stripe border>
        <el-table-column prop="name" label="姓名" sortable />
        <el-table-column prop="age" label="年龄" sortable />
        <el-table-column prop="address" label="地址" show-overflow-tooltip />
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <el-button size="mini" type="primary">编辑</el-button>
            <el-button size="mini" type="danger">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 固定表头的表格 -->
    <el-card class="test-card">
      <div slot="header">
        <span>固定表头表格</span>
      </div>
      <el-table :data="longTableData" style="width: 100%" height="250">
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="age" label="年龄" width="80" />
        <el-table-column prop="address" label="地址" />
        <el-table-column prop="phone" label="电话" width="150" />
        <el-table-column prop="email" label="邮箱" width="200" />
      </el-table>
    </el-card>

    <!-- 可展开的表格 -->
    <el-card class="test-card">
      <div slot="header">
        <span>可展开表格</span>
      </div>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-form label-position="left" inline class="demo-table-expand">
              <el-form-item label="姓名">
                <span>{{ props.row.name }}</span>
              </el-form-item>
              <el-form-item label="年龄">
                <span>{{ props.row.age }}</span>
              </el-form-item>
              <el-form-item label="地址">
                <span>{{ props.row.address }}</span>
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="age" label="年龄" />
        <el-table-column prop="address" label="地址" />
      </el-table>
    </el-card>

    <!-- 分页 -->
    <div class="pagination-demo">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="100"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="10"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'TableTest',
  data() {
    return {
      tableData: [
        { name: '张三', age: 25, address: '北京市朝阳区' },
        { name: '李四', age: 30, address: '上海市浦东新区' },
        { name: '王五', age: 28, address: '广州市天河区' },
        { name: '赵六', age: 32, address: '深圳市南山区' }
      ],
      longTableData: []
    }
  },
  created() {
    // 生成长表格数据
    for (let i = 1; i <= 20; i++) {
      this.longTableData.push({
        name: `用户${i}`,
        age: 20 + Math.floor(Math.random() * 30),
        address: `城市${i} 区域${i} 街道${i}号`,
        phone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
        email: `user${i}@example.com`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-test-page {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;

  h1 {
    font-size: 28px;
    color: #2d3748;
    margin-bottom: 8px;
  }

  p {
    color: #64748b;
    font-size: 16px;
  }
}

.test-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);

  .el-card__header {
    background: #f8fafc;
    font-weight: 600;
    color: #2d3748;
  }
}

.pagination-demo {
  display: flex;
  justify-content: center;
  padding: 24px 0;
}

.demo-table-expand {
  font-size: 0;
}

.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
</style>
