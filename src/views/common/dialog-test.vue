<template>
  <div class="dialog-test-page">
    <div class="page-header">
      <h1>对话框测试</h1>
      <p>测试Element UI对话框的闪烁修复效果</p>
    </div>

    <div class="test-buttons">
      <!-- 基础对话框 -->
      <el-button type="primary" @click="basicDialogVisible = true">
        基础对话框
      </el-button>

      <!-- 表单对话框 -->
      <el-button type="success" @click="formDialogVisible = true">
        表单对话框
      </el-button>

      <!-- 表格对话框 -->
      <el-button type="info" @click="tableDialogVisible = true">
        表格对话框
      </el-button>

      <!-- 嵌套对话框 -->
      <el-button type="warning" @click="nestedDialogVisible = true">
        嵌套对话框
      </el-button>

      <!-- 全屏对话框 -->
      <el-button type="danger" @click="fullscreenDialogVisible = true">
        全屏对话框
      </el-button>

      <!-- 自适应对话框 -->
      <el-button @click="adaptiveDialogVisible = true">
        自适应对话框
      </el-button>
    </div>

    <!-- 基础对话框 -->
    <el-dialog
      title="基础对话框"
      :visible.sync="basicDialogVisible"
      width="30%"
    >
      <span>这是一个基础对话框，测试是否有闪烁现象。</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="basicDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="basicDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 表单对话框 -->
    <el-dialog
      title="表单对话框"
      :visible.sync="formDialogVisible"
      width="50%"
    >
      <el-form :model="form" label-width="80px">
        <el-form-item label="姓名">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="年龄">
          <el-input v-model="form.age" />
        </el-form-item>
        <el-form-item label="城市">
          <el-select v-model="form.city" placeholder="请选择城市">
            <el-option label="北京" value="beijing" />
            <el-option label="上海" value="shanghai" />
            <el-option label="广州" value="guangzhou" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="formDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="formDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 表格对话框 -->
    <el-dialog
      title="表格对话框"
      :visible.sync="tableDialogVisible"
      width="70%"
    >
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="age" label="年龄" />
        <el-table-column prop="address" label="地址" />
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="tableDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 嵌套对话框 -->
    <el-dialog
      title="外层对话框"
      :visible.sync="nestedDialogVisible"
      width="50%"
    >
      <span>这是外层对话框</span>
      <el-button type="primary" @click="innerDialogVisible = true">
        打开内层对话框
      </el-button>
      
      <el-dialog
        title="内层对话框"
        :visible.sync="innerDialogVisible"
        width="30%"
        append-to-body
      >
        <span>这是内层对话框，测试嵌套是否正常。</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="innerDialogVisible = false">关 闭</el-button>
        </span>
      </el-dialog>
      
      <span slot="footer" class="dialog-footer">
        <el-button @click="nestedDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 全屏对话框 -->
    <el-dialog
      title="全屏对话框"
      :visible.sync="fullscreenDialogVisible"
      fullscreen
    >
      <div style="height: 400px; display: flex; align-items: center; justify-content: center;">
        <h2>全屏对话框内容</h2>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="fullscreenDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 自适应对话框 -->
    <el-dialog
      title="自适应对话框"
      :visible.sync="adaptiveDialogVisible"
      width="60%"
      class="adaptive_dialog"
    >
      <div style="height: 600px; overflow-y: auto;">
        <p v-for="i in 50" :key="i">
          这是第 {{ i }} 行内容，用于测试自适应对话框的滚动效果。
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="adaptiveDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DialogTest',
  data() {
    return {
      basicDialogVisible: false,
      formDialogVisible: false,
      tableDialogVisible: false,
      nestedDialogVisible: false,
      innerDialogVisible: false,
      fullscreenDialogVisible: false,
      adaptiveDialogVisible: false,
      form: {
        name: '',
        age: '',
        city: ''
      },
      tableData: [
        { name: '张三', age: 25, address: '北京市朝阳区' },
        { name: '李四', age: 30, address: '上海市浦东新区' },
        { name: '王五', age: 28, address: '广州市天河区' }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-test-page {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;

  h1 {
    font-size: 28px;
    color: #2d3748;
    margin-bottom: 8px;
  }

  p {
    color: #64748b;
    font-size: 16px;
  }
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: center;
  margin-bottom: 40px;

  .el-button {
    min-width: 120px;
  }
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .test-buttons {
    flex-direction: column;
    align-items: center;
  }
}
</style>
