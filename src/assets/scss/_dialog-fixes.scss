// ==========================================================================
// Element UI Dialog Fixes - 修复对话框闪烁问题
// ==========================================================================

// ==========================================================================
// 对话框动画优化 - 防止闪烁
// ==========================================================================

// Element UI 对话框遮罩层优化
.el-dialog__wrapper {
  // 防止闪烁的关键属性
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  perspective: 1000px;
  -webkit-perspective: 1000px;
  
  // 确保动画流畅
  will-change: opacity;
  
  // 覆盖可能冲突的动画
  &.v-enter-active,
  &.v-leave-active {
    transition: opacity 0.3s ease !important;
  }
  
  &.v-enter,
  &.v-leave-to {
    opacity: 0 !important;
  }
}

// 对话框本体优化
.el-dialog {
  // 防止闪烁的核心属性
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: transform, opacity;
  
  // 确保动画性能
  &.v-enter-active,
  &.v-leave-active {
    transition: all 0.3s ease !important;
    transform: translateZ(0) !important;
  }
  
  &.v-enter,
  &.v-leave-to {
    opacity: 0 !important;
    transform: translateZ(0) scale(0.7) !important;
  }
  
  // 防止内容闪烁
  .el-dialog__header,
  .el-dialog__body,
  .el-dialog__footer {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }
}

// ==========================================================================
// 修复特定的动画冲突
// ==========================================================================

// 禁用可能冲突的全局动画
.el-dialog,
.el-dialog__wrapper {
  // 移除可能冲突的动画类
  &.fade-enter-active,
  &.fade-leave-active,
  &.slide-fade-enter-active,
  &.slide-fade-leave-active,
  &.scale-enter-active,
  &.scale-leave-active {
    animation: none !important;
  }
  
  // 移除可能冲突的动画状态
  &.fade-enter,
  &.fade-leave-to,
  &.slide-fade-enter,
  &.slide-fade-leave-to,
  &.scale-enter,
  &.scale-leave-to {
    animation: none !important;
  }
}

// ==========================================================================
// 对话框内容优化
// ==========================================================================

// 防止表单元素闪烁
.el-dialog {
  .el-form,
  .el-form-item,
  .el-input,
  .el-select,
  .el-button {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
  
  // 防止表格闪烁
  .el-table {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform: translateZ(0);
  }
  
  // 防止其他组件闪烁
  .el-card,
  .el-tabs,
  .el-collapse {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
}

// ==========================================================================
// 移动端优化
// ==========================================================================
@media (max-width: 768px) {
  .el-dialog {
    // 移动端性能优化
    transform: translate3d(0, 0, 0);
    -webkit-transform: translate3d(0, 0, 0);
    
    &.v-enter-active,
    &.v-leave-active {
      transition: all 0.2s ease !important;
    }
  }
}

// ==========================================================================
// 浏览器兼容性修复
// ==========================================================================

// Chrome/Safari 优化
@supports (-webkit-appearance: none) {
  .el-dialog {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

// Firefox 优化
@-moz-document url-prefix() {
  .el-dialog {
    transform: translateZ(0);
  }
}

// ==========================================================================
// 特殊情况处理
// ==========================================================================

// 嵌套对话框优化
.el-dialog .el-dialog {
  z-index: 2001;
  
  &__wrapper {
    z-index: 2001;
  }
}

// 全屏对话框优化
.el-dialog.is-fullscreen {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform: none !important;
  -webkit-transform: none !important;
}

// 加载状态优化
.el-dialog .el-loading-mask {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}
