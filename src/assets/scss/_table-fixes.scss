// ==========================================================================
// Element UI Table Fixes - 修复表格样式问题
// ==========================================================================

// ==========================================================================
// 重置表格基础样式，确保Element UI原有功能正常
// ==========================================================================

.el-table {
  // 确保表格基础样式不被覆盖
  color: inherit;
  
  // 表格容器
  &__header-wrapper,
  &__body-wrapper,
  &__footer-wrapper {
    overflow-x: auto;
  }

  // 表头样式重置
  &__header {
    width: 100%;
    
    th {
      // 保持Element UI原有的表头样式
      position: relative;
      box-sizing: border-box;
      text-overflow: ellipsis;
      vertical-align: middle;
      text-align: left;
      
      // 温和的样式增强
      &.el-table__cell {
        background-color: #fafafa;
        font-weight: 500;
        color: #606266;
        border-bottom: 1px solid #ebeef5;
        padding: 12px 0;
        font-size: 13px;
        
        // 保持排序功能正常
        &.is-sortable {
          cursor: pointer;
          
          &:hover {
            background-color: #f5f7fa;
          }
        }
        
        // 排序图标样式
        .caret-wrapper {
          display: inline-flex;
          flex-direction: column;
          align-items: center;
          height: 14px;
          width: 24px;
          vertical-align: middle;
          cursor: pointer;
          overflow: initial;
          position: relative;
        }
      }
    }
  }

  // 表格主体样式
  &__body {
    width: 100%;
    
    tr {
      // 保持原有的行样式
      &:hover > td {
        background-color: #f5f7fa !important;
      }
      
      // 选中行样式
      &.current-row > td {
        background-color: #ecf5ff !important;
      }
    }
    
    td {
      // 保持Element UI原有的单元格样式
      &.el-table__cell {
        box-sizing: border-box;
        text-overflow: ellipsis;
        vertical-align: middle;
        position: relative;
        text-align: left;
        border-bottom: 1px solid #ebeef5;
        padding: 12px 0;
        min-width: 0;
        
        // 单元格内容
        .cell {
          box-sizing: border-box;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: normal;
          word-break: break-all;
          line-height: 23px;
          padding-left: 10px;
          padding-right: 10px;
        }
      }
    }
  }

  // 固定列样式
  &__fixed,
  &__fixed-right {
    position: absolute;
    top: 0;
    left: 0;
    overflow-x: hidden;
    overflow-y: hidden;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      pointer-events: none;
    }
  }

  &__fixed-right {
    left: auto;
    right: 0;
  }

  // 空数据样式
  &__empty-block {
    min-height: 60px;
    text-align: center;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__empty-text {
    line-height: 60px;
    width: 50%;
    color: #909399;
  }

  // 展开行样式
  &__expand-column {
    .cell {
      padding: 0;
      text-align: center;
    }
  }

  &__expand-icon {
    position: relative;
    cursor: pointer;
    color: #666;
    font-size: 12px;
    transition: transform 0.2s ease-in-out;
    height: 20px;
    
    &--expanded {
      transform: rotate(90deg);
    }
    
    > .el-icon {
      position: absolute;
      left: 50%;
      top: 50%;
      margin-left: -5px;
      margin-top: -5px;
    }
  }

  // 树形表格样式
  &__indent {
    display: inline-block;
  }

  &__placeholder {
    display: inline-block;
    width: 20px;
  }
}

// ==========================================================================
// 表格分页样式修复
// ==========================================================================
.el-table + .el-pagination {
  margin-top: 16px;
  text-align: right;
}

// ==========================================================================
// 响应式表格优化
// ==========================================================================
@media (max-width: 768px) {
  .el-table {
    font-size: 12px;
    
    th.el-table__cell,
    td.el-table__cell {
      padding: 8px 0;
      
      .cell {
        padding-left: 8px;
        padding-right: 8px;
      }
    }
  }
}

// ==========================================================================
// 表格加载状态样式
// ==========================================================================
.el-table .el-loading-mask {
  position: absolute;
  z-index: 2000;
  background-color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

// ==========================================================================
// 表格工具栏样式
// ==========================================================================
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0;
  
  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}
