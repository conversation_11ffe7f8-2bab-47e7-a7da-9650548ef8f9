// ==========================================================================
// Global Styles - Component Overrides & Utilities
// ==========================================================================

// ==========================================================================
// Element UI - Dialog Adaptive
// ==========================================================================
// 解决 Element UI Dialog 内容过长的问题
// 后期可考虑用 JS 动态计算 Dialog 高度（包含 resize 情况）
.adaptive_dialog {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .el-dialog {
    margin: 0 auto !important;
    height: 90%;
    overflow: hidden;

    .el-dialog__body {
      max-height: calc(100% - 54px - 66px);
      box-sizing: border-box;
      overflow: hidden auto;
    }
  }
}

// ==========================================================================
// Element UI - Table Customization
// ==========================================================================
.el-table th.el-table__cell {
  background-color: #fafafa;
  padding: 10px 0;
  font-size: 13px;
}

// ==========================================================================
// Vue Router - Transition Animations
// ==========================================================================

// Fade transition
.fade-enter-active,
.fade-leave-active {
  transition: opacity $--transition-slow;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

// Slide fade transition
.slide-fade-enter-active {
  transition: $--transition-base;
}

.slide-fade-leave-active {
  transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}

// ==========================================================================
// Utility Classes
// ==========================================================================

// Page table height utility
.page-table-height {
  height: calc(100vh - 135px);
}
