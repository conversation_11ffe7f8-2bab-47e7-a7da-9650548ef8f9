/* 解决element-ui/dialog太长的情况
  （后期优化）
  可考虑用JS动态计算其dialog的高度
  （包含resize情况）
  自动添加class
------------------------------ */
.adaptive_dialog {
  display: flex;
  justify-content: center;
  align-items: Center;
  overflow: hidden;

  .el-dialog {
    margin: 0 auto !important;
    height: 90%;
    overflow: hidden;

    .el-dialog__body {
      max-height: calc(100% - 54px - 66px);
      box-sizing: border-box;
      overflow: hidden auto;
    }
  }
}

/*
element-ui
------------------------*/
//.el-popover {
//  min-width: 300px;
//}
//
//.el-table th.gutter {
//  display: table-cell !important;
//}

.el-table th.el-table__cell {
  background-color: #fafafa;
  padding: 10px 0;
  font-size: 13px;
}

/*
路由动画
------------------------*/
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}

/* 可以设置不同的进入和离开动画 */
/* 设置持续时间和动画函数 */
.slide-fade-enter-active {
  transition: all 0.3s ease;
}
.slide-fade-leave-active {
  transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter, .slide-fade-leave-to
  /* .slide-fade-leave-active for below version 2.1.8 */ {
  transform: translateX(10px);
  opacity: 0;
}

.page-table-height {
  height: Calc(100vh - 135px);
}
