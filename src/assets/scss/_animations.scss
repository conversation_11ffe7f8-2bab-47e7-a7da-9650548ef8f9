// ==========================================================================
// Animations - 现代化动画效果
// ==========================================================================

// ==========================================================================
// 关键帧动画定义
// ==========================================================================

// 淡入动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 滑入动画
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 滑入动画（左侧）
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 缩放动画
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 弹跳动画
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// 摇摆动画
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

// 渐变背景动画
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// 加载动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// ==========================================================================
// Vue 路由过渡动画增强
// ==========================================================================

// 淡入淡出过渡
.fade-enter-active,
.fade-leave-active {
  transition: opacity $--transition-slow, transform $--transition-slow;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

// 滑动淡入过渡
.slide-fade-enter-active {
  transition: all $--transition-base;
}

.slide-fade-leave-active {
  transition: all 0.4s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

// 缩放过渡
.scale-enter-active,
.scale-leave-active {
  transition: all $--transition-base;
}

.scale-enter,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

// ==========================================================================
// 页面加载动画
// ==========================================================================
.page-enter {
  animation: fadeIn 0.6s ease-out;
}

.card-enter {
  animation: slideInRight 0.4s ease-out;
}

.sidebar-enter {
  animation: slideInLeft 0.5s ease-out;
}

// 模态框动画 - 已移至 _dialog-fixes.scss 避免冲突
// .modal-enter {
//   animation: scaleIn 0.3s ease-out;
// }

// ==========================================================================
// 交互动画类
// ==========================================================================

// 悬停效果
.hover-lift {
  transition: $--transition-transform;
  
  &:hover {
    transform: translateY(-2px);
  }
}

.hover-scale {
  transition: $--transition-transform;
  
  &:hover {
    transform: scale(1.05);
  }
}

.hover-rotate {
  transition: $--transition-transform;
  
  &:hover {
    transform: rotate(5deg);
  }
}

// 点击效果
.click-bounce {
  &:active {
    animation: pulse 0.2s ease-in-out;
  }
}

.click-shake {
  &.shake {
    animation: shake 0.5s ease-in-out;
  }
}

// ==========================================================================
// 加载动画
// ==========================================================================
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  border-top-color: $--color-primary;
  animation: spin 1s ease-in-out infinite;
}

.loading-dots {
  display: inline-block;
  
  &::after {
    content: '...';
    animation: dots 1.5s steps(4, end) infinite;
  }
}

@keyframes dots {
  0%, 20% {
    color: rgba(0, 0, 0, 0);
    text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0),
                 0.5em 0 0 rgba(0, 0, 0, 0);
  }
  40% {
    color: $--color-primary;
    text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0),
                 0.5em 0 0 rgba(0, 0, 0, 0);
  }
  60% {
    text-shadow: 0.25em 0 0 $--color-primary,
                 0.5em 0 0 rgba(0, 0, 0, 0);
  }
  80%, 100% {
    text-shadow: 0.25em 0 0 $--color-primary,
                 0.5em 0 0 $--color-primary;
  }
}

// ==========================================================================
// 特殊效果动画
// ==========================================================================

// 渐变背景动画
.gradient-bg {
  background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

// 浮动效果
.floating {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0% {
    transform: translate(0, 0px);
  }
  50% {
    transform: translate(0, -10px);
  }
  100% {
    transform: translate(0, 0px);
  }
}

// 闪烁效果
.blink {
  animation: blink 2s linear infinite;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

// ==========================================================================
// 响应式动画控制
// ==========================================================================
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// 移动端动画优化
@media (max-width: 768px) {
  .fade-enter-active,
  .fade-leave-active,
  .slide-fade-enter-active,
  .slide-fade-leave-active {
    transition-duration: 0.2s;
  }
}
