// ==========================================================================
// SCSS Variables - Design System
// ==========================================================================

// ==========================================================================
// Colors - Primary Palette
// ==========================================================================
// 站点主色
// Tips: 要达到整站主题修改效果，请确保 $--color-primary 与 element-ui-theme 中的主题色一致
$--color-primary: #409eff;
$--color-primary-light: lighten($--color-primary, 10%);
$--color-primary-dark: darken($--color-primary, 10%);

// ==========================================================================
// Colors - Semantic Colors
// ==========================================================================
$--color-success: #67c23a;
$--color-warning: #e6a23c;
$--color-danger: #f56c6c;
$--color-info: #909399;

// ==========================================================================
// Colors - Neutral Colors
// ==========================================================================
$--color-text-primary: #303133;
$--color-text-regular: #606266;
$--color-text-secondary: #909399;
$--color-text-placeholder: #c0c4cc;

$--color-white: #ffffff;
$--color-black: #000000;

$--border-color-base: #dcdfe6;
$--border-color-light: #e4e7ed;
$--border-color-lighter: #ebeef5;
$--border-color-extra-light: #f2f6fc;

$--background-color-base: #f5f7fa;

// ==========================================================================
// Layout - Navbar
// ==========================================================================
$navbar--background-color: $--color-primary;
$navbar--height: 50px;
$navbar--text-color: $--color-white;

// ==========================================================================
// Layout - Sidebar
// ==========================================================================
$sidebar--background-color-dark: #00162a;
$sidebar--color-text-dark: #b7bdc3;
$sidebar--width: 230px;
$sidebar--width-collapsed: 64px;

// ==========================================================================
// Layout - Content
// ==========================================================================
$content--background-color: #f1f4f5;

// ==========================================================================
// Typography
// ==========================================================================
$--font-family-primary: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
$--font-size-base: 14px;
$--font-size-small: 12px;
$--font-size-large: 16px;
$--font-size-extra-large: 18px;

$--line-height-base: 1.15;

// ==========================================================================
// Spacing
// ==========================================================================
$--spacing-xs: 4px;
$--spacing-sm: 8px;
$--spacing-md: 12px;
$--spacing-lg: 16px;
$--spacing-xl: 20px;
$--spacing-xxl: 24px;

// ==========================================================================
// Border Radius
// ==========================================================================
$--border-radius-base: 4px;
$--border-radius-small: 2px;
$--border-radius-large: 6px;

// ==========================================================================
// Shadows
// ==========================================================================
$--box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$--box-shadow-light: 0 1px 1px rgba(0, 0, 0, 0.05);

// ==========================================================================
// Transitions
// ==========================================================================
$--transition-base: all 0.3s ease;
$--transition-fast: all 0.2s ease;
$--transition-slow: all 0.5s ease;
