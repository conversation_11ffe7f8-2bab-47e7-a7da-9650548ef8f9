// ==========================================================================
// UI Enhancements - 现代化UI美观度提升
// ==========================================================================

// ==========================================================================
// 卡片组件增强
// ==========================================================================
.el-card {
  border: none;
  border-radius: $--border-radius-large;
  box-shadow: $content--card-shadow;
  transition: $--transition-base;
  overflow: hidden;

  &:hover {
    box-shadow: $content--card-shadow-hover;
    transform: translateY(-2px);
  }

  .el-card__header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid $--border-color-extra-light;
    padding: $--spacing-lg $--spacing-xl;
    font-weight: 600;
    color: $--color-text-primary;
  }

  .el-card__body {
    padding: $--spacing-xl;
  }
}

// ==========================================================================
// 按钮组件增强
// ==========================================================================
.el-button {
  border-radius: $--border-radius-base;
  font-weight: 500;
  transition: $--transition-colors, $--transition-transform;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  // 主要按钮样式
  &--primary {
    background: $--color-primary-gradient;
    border: none;
    box-shadow: $--box-shadow-colored;

    &:hover {
      box-shadow: $--box-shadow-md;
    }
  }

  // 成功按钮
  &--success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border: none;
  }

  // 警告按钮
  &--warning {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    border: none;
  }

  // 危险按钮
  &--danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    border: none;
  }
}

// ==========================================================================
// 表格组件增强 - 最小化干预版本
// ==========================================================================
.el-table {
  // 仅添加轻微的视觉增强，不改变布局和功能
  border-radius: $--border-radius-small;

  // 轻微的阴影效果
  &:not(.el-table--border) {
    box-shadow: $--box-shadow-sm;
  }

  // 悬停效果增强（不使用!important，避免冲突）
  .el-table__body tr:hover > td.el-table__cell {
    background-color: rgba(102, 126, 234, 0.02);
  }
}

// ==========================================================================
// 表单组件增强
// ==========================================================================
.el-form-item {
  margin-bottom: $--spacing-xl;

  .el-form-item__label {
    font-weight: 500;
    color: $--color-text-primary;
  }
}

.el-input {
  .el-input__inner {
    border-radius: $--border-radius-base;
    border: 2px solid $--border-color-light;
    transition: $--transition-colors;
    padding: $--spacing-md $--spacing-lg;

    &:focus {
      border-color: $--color-primary;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    &:hover {
      border-color: $--color-primary-light;
    }
  }
}

.el-select {
  .el-input__inner {
    border-radius: $--border-radius-base;
    border: 2px solid $--border-color-light;
    transition: $--transition-colors;

    &:focus {
      border-color: $--color-primary;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }
}

// ==========================================================================
// 分页组件增强
// ==========================================================================
.el-pagination {
  .el-pager li {
    border-radius: $--border-radius-base;
    margin: 0 2px;
    transition: $--transition-colors;

    &:hover {
      background-color: $--color-primary-light;
      color: $--color-white;
    }

    &.active {
      background: $--color-primary-gradient;
      color: $--color-white;
    }
  }

  .btn-prev,
  .btn-next {
    border-radius: $--border-radius-base;
    transition: $--transition-colors;

    &:hover {
      background-color: $--color-primary-light;
      color: $--color-white;
    }
  }
}

// ==========================================================================
// 菜单组件增强
// ==========================================================================
.el-menu {
  border: none;

  .el-menu-item,
  .el-submenu__title {
    transition: $--transition-colors;
    border-radius: $--border-radius-base;
    margin: 2px 4px;

    &:hover {
      background-color: rgba(102, 126, 234, 0.1);
      color: $--color-primary;
    }

    &.is-active {
      background: $--color-primary-gradient;
      color: $--color-white;
      font-weight: 500;
    }
  }
}

// ==========================================================================
// 标签页组件增强
// ==========================================================================
.el-tabs {
  .el-tabs__header {
    .el-tabs__nav-wrap {
      .el-tabs__nav {
        .el-tabs__item {
          border-radius: $--border-radius-base $--border-radius-base 0 0;
          transition: $--transition-colors;
          font-weight: 500;

          &:hover {
            color: $--color-primary;
          }

          &.is-active {
            background: $--color-white;
            color: $--color-primary;
            border-color: $--color-primary;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              bottom: -1px;
              left: 0;
              right: 0;
              height: 2px;
              background: $--color-primary-gradient;
            }
          }
        }
      }
    }
  }
}

// ==========================================================================
// 对话框组件增强
// ==========================================================================
.el-dialog {
  border-radius: $--border-radius-large;
  box-shadow: $--box-shadow-xl;
  overflow: hidden;

  .el-dialog__header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: $--spacing-xl;
    border-bottom: 1px solid $--border-color-extra-light;

    .el-dialog__title {
      font-weight: 600;
      color: $--color-text-primary;
    }
  }

  .el-dialog__body {
    padding: $--spacing-xl;
  }

  .el-dialog__footer {
    padding: $--spacing-lg $--spacing-xl;
    background-color: $--background-color-base;
    border-top: 1px solid $--border-color-extra-light;
  }
}

// ==========================================================================
// 消息提示增强
// ==========================================================================
.el-message {
  border-radius: $--border-radius-base;
  box-shadow: $--box-shadow-lg;
  border: none;
}

.el-notification {
  border-radius: $--border-radius-large;
  box-shadow: $--box-shadow-xl;
  border: none;
}
