*,
*:before,
*:after {
  box-sizing: border-box;
}
body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
  font-size: 14px;
  line-height: 1.15;
  color: #303133;
  background-color: #fff;
}
a {
  color: mix(#fff, $--color-primary, 20%);
  text-decoration: none;
  &:focus,
  &:hover {
    color: $--color-primary;
    text-decoration: underline;
  }
}
img {
  vertical-align: middle;
}

/* Utils
------------------------------ */
.clearfix:before,
.clearfix:after {
  content: ' ';
  display: table;
}
.clearfix:after {
  clear: both;
}

/* Animation
------------------------------ */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}

/* Reset element-ui
------------------------------ */
.site-wrapper {
  .el-pagination {
    margin-top: 15px;
    text-align: right;
  }
}

/* Layout
------------------------------ */
.site-wrapper {
  position: relative;
  min-width: 1180px;

  .site-navbar__header {
    //background: $sidebar--background-color-dark;
  }
}

/* Sidebar fold
------------------------------ */
.site-sidebar--fold {
  .site-navbar__header,
  .site-navbar__brand,
  .site-sidebar,
  .site-sidebar__inner,
  .el-menu.site-sidebar__menu {
    width: 64px;
  }
  .site-navbar__body,
  .site-content__wrapper {
    margin-left: 64px;
  }
  .site-navbar__brand {
    &-lg {
      display: none;
    }
    &-mini {
      display: inline-block;
    }
  }
  .site-sidebar,
  .site-sidebar__inner {
    overflow: initial;
  }
  .site-sidebar__menu-icon {
    margin-right: 0;
    font-size: 20px;
  }
  .site-content--tabs > .el-tabs > .el-tabs__header {
    left: 64px;
  }
}
// animation
.site-navbar__header,
.site-navbar__brand,
.site-navbar__body,
.site-sidebar,
.site-sidebar__inner,
.site-sidebar__menu.el-menu,
.site-sidebar__menu-icon,
.site-content__wrapper,
.site-content--tabs > .el-tabs .el-tabs__header {
  transition: inline-block 0.3s, left 0.3s, width 0.3s, margin-left 0.3s,
    font-size 0.3s;
}

/* Navbar
------------------------------ */
.site-navbar {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
  height: 50px;
  //box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  background-color: $navbar--background-color;

  &--inverse {
    .site-navbar__body {
      //background-color: transparent;
    }
    .el-menu {
      background-color: $navbar--background-color;

      > .el-menu-item,
      > .el-submenu > .el-submenu__title {
        color: #fff;
        &:focus,
        &:hover {
          //color: #fff;
          //background-color: mix(#000, $navbar--background-color, 15%);
        }
      }
      > .el-menu-item.is-active,
      > .el-submenu.is-active > .el-submenu__title {
        //border-bottom-color: mix(#fff, $navbar--background-color, 85%);
      }
      .el-menu-item i,
      .el-submenu__title i,
      .el-dropdown {
        //color: #fff;
      }
    }
    .el-menu--popup-bottom-start {
      //background-color: $navbar--background-color;
    }
  }

  &__header {
    position: relative;
    float: left;
    width: 230px;
    height: 50px;
    overflow: hidden;
  }
  &__brand {
    display: table-cell;
    vertical-align: middle;
    width: 230px;
    height: 50px;
    margin: 0;
    line-height: 50px;
    font-size: 20px;
    text-align: center;
    text-transform: uppercase;
    white-space: nowrap;
    color: #fff;

    &-lg,
    &-mini {
      margin: 0 5px;
      color: #fff;
      &:focus,
      &:hover {
        color: #fff;
        text-decoration: none;
      }
    }
    &-mini {
      display: none;
    }
  }
  &__switch {
    font-size: 18px;
    border-bottom: none !important;
  }
  &__avatar {
    border-bottom: none !important;
    * {
      vertical-align: inherit;
    }
    .el-dropdown-link {
      > img {
        width: 36px;
        height: auto;
        margin-right: 5px;
        border-radius: 100%;
        vertical-align: middle;
      }
    }
  }
  &__body {
    position: relative;
    margin-left: 230px;
    padding-right: 15px;
    background-color: #fff;
  }
  &__menu {
    float: left;
    background-color: transparent;
    border-bottom: 0;

    &--right {
      float: right;
    }
    a:focus,
    a:hover {
      text-decoration: none;
    }
    .el-menu-item,
    .el-submenu > .el-submenu__title {
      height: 50px;
      line-height: 50px;
    }
    .el-submenu > .el-menu {
      top: 55px;
    }
    .el-badge {
      display: inline;
      z-index: 2;
      &__content {
        line-height: 16px;
      }
    }
  }
}

/* Sidebar
------------------------------ */
.site-sidebar {
  position: fixed;
  //top: 50px;
  top: 0;
  left: 0;
  //bottom: 0;
  z-index: 1020;
  //width: 230px;
  width: 100%;
  height: 60px;
  background-color: $sidebar--background-color-dark;
  overflow: hidden;
  &--scroll{
    max-height: 650px;
    overflow-y: auto;
    -ms-overflow-style: none;
  }
  &--scroll::-webkit-scrollbar {
    display: none;
  }
    
  &--dark,
  &--dark-popper {
    background-color: $sidebar--background-color-dark;
    .site-sidebar__menu.el-menu,
    > .el-menu--popup {
      background-color: $sidebar--background-color-dark;
      .el-menu-item,
      .el-submenu > .el-submenu__title {
        color: $sidebar--color-text-dark;
        background-color: $sidebar--background-color-dark;
        &:focus,
        &:hover {
          color: mix(#fff, $sidebar--color-text-dark, 50%);
          background-color: mix(#fff, $sidebar--background-color-dark, 2.5%);
        }
      }
      .el-menu,
      .el-submenu.is-opened {
        background-color: mix(#000, $sidebar--background-color-dark, 15%);
      }
      .el-menu-item.is-active,
      .el-submenu.is-active > .el-submenu__title {
        color: mix(#fff, $sidebar--color-text-dark, 80%);
      }

      .el-menu-item {
        background-color: $sidebar--background-color-dark;
      }
    }
  }
  &__inner {
    position: relative;
    z-index: 1;
    //width: 250px;
    width: 100%;
    height: 60px;
    //height: 100%;
    //padding-bottom: 15px;
    //overflow-y: scroll;
  }
  &__menu.el-menu {
    //width: 230px;
    border-right: 0;
  }
  &__menu-icon {
    //width: 24px;
    margin-right: 5px;
    text-align: center;
    font-size: 16px;
    //color: inherit !important;
  }
}

/* Content
------------------------------ */
.site-content {
  position: relative;
  // padding: 15px;

  &__wrapper {
    position: relative;
    // padding-top: 60px;
    //margin-left: 230px;
    min-height: 100%;
    background: $content--background-color;
  }
  &--tabs {
    // padding: 40px 0 0;
  }
  > .el-tabs {
    > .el-tabs__header {
      // position: fixed;
      // top: 60px;
      left: 0;
      right: 0;
      z-index: 930;
      padding: 0 55px 0 15px;
      //box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
      box-shadow: 0 1px 1px rgb(0 0 0 / 5%);
      background-color: #fff;
      > .el-tabs__nav-wrap {
        margin-bottom: 0;
        &:after {
          display: none;
        }

        .el-tabs__nav {
          border: none;
          border-radius: 0;
          .el-tabs__item + .el-tabs__item {
            margin-left: 10px;
          }

          .el-tabs__item {
            font-size: 12px;
            padding: 0 10px;
            line-height: 30px;
            height: 30px;
            border: 1px solid #e4e7ed;
            border-bottom: none;
          }
        }
      }
    }
    > .el-tabs__content {
      padding: 0 15px 15px;
      > .site-tabs__tools {
        position: fixed;
        top: 60px;
        right: 0;
        z-index: 931;
        height: 30px;
        padding: 0 12px;
        font-size: 16px;
        line-height: 30px;
        background-color: $content--background-color;
        cursor: pointer;
        .el-icon--right {
          margin-left: 0;
        }
      }
    }
  }
}
.el-table__expand-icon {
  display: inline-block;
  width: 14px;
  vertical-align: middle;
  margin-right: 5px;
}
